// === Jenkins<PERSON>le (CSV fan-out with NUM_WORKERS parallel workers) ===

DATASET_NAME = "csv_location"

def getFileNameWithoutExtension(String filename) {
    return filename.lastIndexOf('.') != -1 ? filename[0..filename.lastIndexOf('.') - 1] : filename
}

def encloseWithQuotes(x) {
    return "\"${x}\""
}

def prepareDataset() {
    try {
        unstash DATASET_NAME
        env.INPUT_FILEPATH = "./input/${csv_location_FILENAME}"
        def directoryPath = env.INPUT_FILEPATH.substring(0, env.INPUT_FILEPATH.lastIndexOf('/'))
        sh """
          mkdir -p ${directoryPath}
          mv ${DATASET_NAME} ${env.INPUT_FILEPATH}
        """
        echo "Prepared CSV at: ${env.INPUT_FILEPATH}"
    } catch (groovy.lang.MissingPropertyException e) {
        error 'You forgot to provide dataset file.'
    }
}

pipeline {
   agent { label "${params.AGENT_LABEL?.trim() ?: 'any'}" }
  options { skipDefaultCheckout(true) }

  environment {
    PIP_DISABLE_PIP_VERSION_CHECK = '1'
    PYBIN = 'python3.11'
    VENV_DIR = '.venv'    // lives inside the shared workspace
  }

  parameters {
    // Required: the uploaded CSV is stashed by the controller under name=DATASET_NAME
    stashedFile(
      name: DATASET_NAME,
      description: 'CSV to process (must contain column "cib_id", used for chunk labels)'
    )
    choice(
      name: 'markdown_db_table',
      choices: ['wget2_runs', 'wget_runs', 'scrapegraph_runs', 'playwright_runs', "imprint_runs"],
      description: 'Which table to take the markdowns from?'
    )
    string(
      name: 'prompt_yaml_filename',
      defaultValue: 'number_of_employees.yaml',
      description: 'YAML file with prompts'
    )
    string(
      name: 'model',
      defaultValue: 'gpt-5-nano-2025-08-07',
      description: 'Which OpenAI model to use'
    )
    string(
      name: 'md_max_length',
      defaultValue: '950_000',
      description: 'First N characters of the markdown to use'
    )
    string(
      name: 'NUM_WORKERS',
      defaultValue: '4',
      description: 'How many parallel workers to spawn (CSV will be split into this many chunks)'
    )
    booleanParam(name: 'reinstall_requirements', defaultValue: false, description: 'If checked, (re)create venv and install requirements')

string(
  name: 'AGENT_LABEL',
  defaultValue: 'built-in || jenkins-slave1',
  description: 'Node label expression. Leave blank for any. Examples: "linux && docker", "gpu || linux-docker"'
)

  }

  stages {
    stage('Checkout'){ steps { checkout scm } }

    stage('Prepare CSV') {
      steps {
        script {
          echo "Using uploaded CSV (${DATASET_NAME})"
          prepareDataset() // will unstash and move to ./input/<original_filename>
        }
      }
    }

    stage('Prepare venv') {
      steps {
        sh '''
          set -eux
          if [ "${reinstall_requirements}" = "true" ]; then
            echo ">> Reinstall requested — recreating ${VENV_DIR}"
            rm -rf "${VENV_DIR}"
          fi

          if [ ! -d "${VENV_DIR}" ]; then
            echo ">> Creating venv at ${VENV_DIR}"
            ${PYBIN} -m venv "${VENV_DIR}"
            . "${VENV_DIR}/bin/activate"
            pip install --no-cache-dir -r requirements.txt
          else
            echo ">> Reusing existing venv at ${VENV_DIR}"
            . "${VENV_DIR}/bin/activate"
            python -V
            pip -V
          fi
        '''
      }
    }

    stage('Split CSV into chunks') {
      steps {
        sh '''
          set -eux
          . "$VENV_DIR/bin/activate"
          mkdir -p chunks
          python scripts/split.py \
            --input-csv "$INPUT_FILEPATH" \
            --chunk-dir "chunks" \
            --workers "${NUM_WORKERS}"
          echo ">> Available chunks:"
          ls -l chunks
          test -s chunks/chunks.txt
        '''
      }
    }

    stage('Fan-out workers') {
      steps {
        script {
          // Read the list of chunk CSV filenames
          def chunkListText = readFile(file: 'chunks/chunks.txt').trim()
          if (!chunkListText) {
            error("No chunks listed in chunks/chunks.txt")
          }
          def chunks = chunkListText.split('\\r?\\n') as List

          echo "Launching ${chunks.size()} parallel workers"

          // Build a map of parallel branches
          def branches = [:]
          for (int i = 0; i < chunks.size(); i++) {
            def idx = i
            def chunkCsv = "chunks/${chunks[i]}"

            branches["worker-${idx+1}"] = {
              // Each branch gets its own stage label (helps in Blue Ocean)
              stage("Worker ${idx+1}") {
                withEnv(["CHUNK_FILE=${chunkCsv}"]) {
                  sh '''
                    set -eux
                    . "$VENV_DIR/bin/activate"
                    python -u -m src.driver \
                      --csv_file "$CHUNK_FILE" \
                      --prompt_yaml_filename "$prompt_yaml_filename" \
                      --markdown_db_table "$markdown_db_table" \
                      --model "$model" \
                      --md_max_length "$md_max_length"
                  '''
                }
              }
            }
          }

          // Run all branches in parallel
          parallel branches
        }
      }
    }
  }
}
